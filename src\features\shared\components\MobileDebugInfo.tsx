'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/supabaseClient';

interface DebugInfo {
  userAgent: string;
  isMobile: boolean;
  hasLocalStorage: boolean;
  hasCookies: boolean;
  supabaseSession: boolean;
  localStorageToken: boolean;
  cookieToken: boolean;
  timestamp: string;
}

export default function MobileDebugInfo() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const collectDebugInfo = async () => {
      const info: DebugInfo = {
        userAgent: navigator.userAgent,
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        hasLocalStorage: typeof Storage !== 'undefined',
        hasCookies: navigator.cookieEnabled,
        supabaseSession: false,
        localStorageToken: false,
        cookieToken: false,
        timestamp: new Date().toISOString(),
      };

      // Verificar sesión de Supabase
      try {
        const { data: { session } } = await supabase.auth.getSession();
        info.supabaseSession = !!session;
      } catch (e) {
        console.warn('Error al verificar sesión de Supabase:', e);
      }

      // Verificar token en localStorage
      try {
        const token = localStorage.getItem('supabase.auth.token');
        info.localStorageToken = !!token;
      } catch (e) {
        console.warn('Error al verificar localStorage:', e);
      }

      // Verificar token en cookies
      try {
        info.cookieToken = document.cookie.includes('supabase.auth.token');
      } catch (e) {
        console.warn('Error al verificar cookies:', e);
      }

      setDebugInfo(info);
    };

    collectDebugInfo();
    
    // Actualizar cada 5 segundos
    const interval = setInterval(collectDebugInfo, 5000);
    
    return () => clearInterval(interval);
  }, []);

  if (!debugInfo) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-500 text-white px-3 py-2 rounded-full text-sm shadow-lg hover:bg-blue-600 transition-colors"
      >
        🐛 Debug
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-black bg-opacity-90 text-white p-4 rounded-lg shadow-xl max-w-sm text-xs">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold text-sm">Debug Info</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-300 hover:text-white"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Móvil:</span>
              <span className={debugInfo.isMobile ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.isMobile ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>LocalStorage:</span>
              <span className={debugInfo.hasLocalStorage ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.hasLocalStorage ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>Cookies:</span>
              <span className={debugInfo.hasCookies ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.hasCookies ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>Sesión Supabase:</span>
              <span className={debugInfo.supabaseSession ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.supabaseSession ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>Token localStorage:</span>
              <span className={debugInfo.localStorageToken ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.localStorageToken ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>Token Cookie:</span>
              <span className={debugInfo.cookieToken ? 'text-green-400' : 'text-red-400'}>
                {debugInfo.cookieToken ? 'Sí' : 'No'}
              </span>
            </div>
            
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-gray-300 break-all">
                <strong>User Agent:</strong><br />
                {debugInfo.userAgent.substring(0, 100)}...
              </div>
            </div>
            
            <div className="text-gray-400 text-xs mt-1">
              Actualizado: {new Date(debugInfo.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
