module.exports = {
  testEnvironment: 'jest-environment-jsdom', // Or 'node' if you prefer, JSDOM for broader compatibility
  preset: 'ts-jest',
  roots: ['<rootDir>/src'],
  moduleNameMapper: {
    // Handle module aliases (if you have them in tsconfig.json)
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    // Mock static assets if needed, e.g., CSS Modules
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'], // Optional: for global setup
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json', // Ensure it points to your tsconfig
    }],
  },
  testPathIgnorePatterns: ['/node_modules/', '/.next/'],
  collectCoverage: true,
  coverageReporters: ['json', 'lcov', 'text', 'clover'],
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/index.ts', // Often just exports, adjust if needed
    '!src/app/layout.tsx', // Example: ignore layout files if not testable
    '!src/app/api/auth/[...nextauth]/route.ts', // Example: ignore auth routes if complex/external
  ],
};
