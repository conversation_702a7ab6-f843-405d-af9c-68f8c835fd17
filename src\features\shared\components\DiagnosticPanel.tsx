import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/supabaseClient';
import { crearConversacion } from '@/lib/supabase/conversacionesService';
import { useAuth } from '@/contexts/AuthContext';

interface DiagnosticInfo {
  supabaseConnection: boolean;
  userAuthenticated: boolean;
  geminiApiKey: boolean;
  conversationsCount: number;
  documentsCount: number;
  lastError: string | null;
}

export default function DiagnosticPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [diagnostics, setDiagnostics] = useState<DiagnosticInfo>({
    supabaseConnection: false,
    userAuthenticated: false,
    geminiApiKey: false,
    conversationsCount: 0,
    documentsCount: 0,
    lastError: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const { user, session } = useAuth();

  const testCreateConversation = async () => {
    try {
      setIsLoading(true);
      const conversationId = await crearConversacion('Conversación de prueba', false);
      if (conversationId) {
        setDiagnostics(prev => ({
          ...prev,
          lastError: `✅ Conversación de prueba creada exitosamente: ${conversationId}`
        }));
        // Actualizar el conteo
        runDiagnostics();
      } else {
        setDiagnostics(prev => ({
          ...prev,
          lastError: '❌ No se pudo crear la conversación de prueba'
        }));
      }
    } catch (error) {
      setDiagnostics(prev => ({
        ...prev,
        lastError: `❌ Error al crear conversación de prueba: ${error instanceof Error ? error.message : 'Unknown error'}`
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const runDiagnostics = async () => {
    setIsLoading(true);
    const newDiagnostics: DiagnosticInfo = {
      supabaseConnection: false,
      userAuthenticated: false,
      geminiApiKey: false,
      conversationsCount: 0,
      documentsCount: 0,
      lastError: null
    };

    try {
      // Verificar autenticación
      newDiagnostics.userAuthenticated = !!user && !!session;

      // Verificar conexión a Supabase
      try {
        // Probar una consulta simple primero
        const { data: testData, error: testError } = await supabase.from('conversaciones').select('id').limit(1);

        if (!testError) {
          newDiagnostics.supabaseConnection = true;

          // Ahora obtener el conteo real de conversaciones del usuario
          const { count, error: countError } = await supabase
            .from('conversaciones')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user && user.id ? user.id : '');

          if (!countError) {
            newDiagnostics.conversationsCount = count || 0;
          } else {
            // Si hay error en el conteo pero la conexión funciona, es probable que sea un problema de RLS
            newDiagnostics.conversationsCount = 0;
            if (countError.code === '406') {
              newDiagnostics.lastError = `Error 406 al contar conversaciones - esto es normal para usuarios nuevos`;
            } else {
              newDiagnostics.lastError = `Error al contar conversaciones: ${countError.message}`;
            }
          }
        } else {
          newDiagnostics.lastError = `Supabase error: ${testError.message}`;
        }
      } catch (error) {
        newDiagnostics.lastError = `Supabase connection error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }

      // Verificar documentos
      try {
        const { data, error } = await supabase.from('documentos').select('count', { count: 'exact', head: true });
        if (!error) {
          newDiagnostics.documentsCount = data?.length || 0;
        }
      } catch (error) {
        console.error('Error checking documents:', error);
      }

      // Verificar API key de Gemini
      newDiagnostics.geminiApiKey = true; // O consulta a una API interna si es necesario

    } catch (error) {
      newDiagnostics.lastError = `General error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }

    setDiagnostics(newDiagnostics);
    setIsLoading(false);
  };

  useEffect(() => {
    if (isOpen) {
      runDiagnostics();
    }
  }, [isOpen]);

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full shadow-lg z-50"
        title="Abrir panel de diagnóstico"
      >
        🔧
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold">Diagnóstico del Sistema</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      {isLoading ? (
        <div className="text-center py-4">
          <div className="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Ejecutando diagnósticos...</p>
        </div>
      ) : (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Usuario autenticado:</span>
            <span className={`text-sm font-semibold ${diagnostics.userAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
              {diagnostics.userAuthenticated ? '✓' : '✗'}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Conexión Supabase:</span>
            <span className={`text-sm font-semibold ${diagnostics.supabaseConnection ? 'text-green-600' : 'text-red-600'}`}>
              {diagnostics.supabaseConnection ? '✓' : '✗'}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">API Key Gemini:</span>
            <span className={`text-sm font-semibold ${diagnostics.geminiApiKey ? 'text-green-600' : 'text-red-600'}`}>
              {diagnostics.geminiApiKey ? '✓' : '✗'}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Conversaciones:</span>
            <span className="text-sm font-semibold text-blue-600">
              {diagnostics.conversationsCount}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Documentos:</span>
            <span className="text-sm font-semibold text-blue-600">
              {diagnostics.documentsCount}
            </span>
          </div>

          {diagnostics.lastError && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
              <p className="text-xs text-red-700 font-semibold">Último error:</p>
              <p className="text-xs text-red-600 mt-1">{diagnostics.lastError}</p>
            </div>
          )}

          <div className="mt-4 space-y-2">
            <div className="flex space-x-2">
              <button
                onClick={runDiagnostics}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded"
                disabled={isLoading}
              >
                Actualizar
              </button>
              <button
                onClick={() => {
                  console.log('Diagnósticos completos:', diagnostics);
                  console.log('Usuario:', user);
                  console.log('Sesión:', session);
                }}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded"
              >
                Log Info
              </button>
            </div>
            <button
              onClick={testCreateConversation}
              className="w-full bg-green-500 hover:bg-green-600 text-white text-xs py-2 px-3 rounded"
              disabled={isLoading || !diagnostics.userAuthenticated}
            >
              Probar Crear Conversación
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
