import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/features/auth/services/authService';
import { PlanificacionUsuario, EstimacionTema } from '@/lib/supabase/supabaseClient';

/**
 * Verifica si el usuario tiene una planificación configurada
 */
export async function tienePlanificacionConfigurada(temarioId: string): Promise<boolean> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.log('No hay usuario autenticado o error:', authError);
      return false;
    }

    // Primero verificar que el temario existe y está activo
    const { data: temarioData, error: temarioError } = await supabase
      .from('temarios')
      .select('id, activo')
      .eq('id', temarioId)
      .eq('user_id', user.id)
      .eq('activo', true)
      .single();

    if (temarioError || !temarioData) {
      console.log('Temario no encontrado o inactivo:', temarioId);
      return false;
    }

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('id')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .eq('completado', true)
      .limit(1);

    if (error) {
      console.error('Error al verificar planificación:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error al verificar planificación:', error);
    return false;
  }
}

/**
 * Obtiene la planificación del usuario para un temario
 */
export async function obtenerPlanificacionUsuario(temarioId: string): Promise<PlanificacionUsuario | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return null;
    }

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('*')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No hay planificación
      }
      console.error('Error al obtener planificación:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener planificación:', error);
    return null;
  }
}

/**
 * Crea o actualiza la planificación del usuario
 */
export async function guardarPlanificacionUsuario(
  temarioId: string,
  planificacion: {
    tiempo_diario_promedio?: number;
    tiempo_por_dia?: Record<string, number>;
    fecha_examen?: string;
    fecha_examen_aproximada?: string;
    familiaridad_general?: number;
    preferencias_horario?: string[];
    frecuencia_repasos?: string;
  }
): Promise<string | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.error('No hay usuario autenticado');
      return null;
    }

    // Verificar si ya existe una planificación
    const planificacionExistente = await obtenerPlanificacionUsuario(temarioId);

    if (planificacionExistente) {
      // Actualizar planificación existente
      const { data, error } = await supabase
        .from('planificacion_usuario')
        .update({
          ...planificacion,
          completado: true,
          actualizado_en: new Date().toISOString()
        })
        .eq('id', planificacionExistente.id)
        .select()
        .single();

      if (error) {
        console.error('Error al actualizar planificación:', error);
        return null;
      }

      return data.id;
    } else {
      // Crear nueva planificación
      const { data, error } = await supabase
        .from('planificacion_usuario')
        .insert([{
          user_id: user.id,
          temario_id: temarioId,
          ...planificacion,
          completado: true
        }])
        .select()
        .single();

      if (error) {
        console.error('Error al crear planificación:', error);
        return null;
      }

      return data.id;
    }
  } catch (error) {
    console.error('Error al guardar planificación:', error);
    return null;
  }
}

/**
 * Obtiene las estimaciones de temas para una planificación
 */
export async function obtenerEstimacionesTemas(planificacionId: string): Promise<EstimacionTema[]> {
  try {
    const { data, error } = await supabase
      .from('estimaciones_temas')
      .select('*')
      .eq('planificacion_id', planificacionId);

    if (error) {
      console.error('Error al obtener estimaciones de temas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener estimaciones de temas:', error);
    return [];
  }
}

/**
 * Guarda las estimaciones de temas
 */
export async function guardarEstimacionesTemas(
  planificacionId: string,
  estimaciones: Array<{
    tema_id: string;
    horas_estimadas?: number;
    es_dificil: boolean;
    es_muy_importante: boolean;
    ya_dominado: boolean;
    notas?: string;
  }>
): Promise<boolean> {
  try {
    // Eliminar estimaciones existentes
    await supabase
      .from('estimaciones_temas')
      .delete()
      .eq('planificacion_id', planificacionId);

    // Insertar nuevas estimaciones
    const estimacionesConPlanificacion = estimaciones.map(est => ({
      ...est,
      planificacion_id: planificacionId
    }));

    const { error } = await supabase
      .from('estimaciones_temas')
      .insert(estimacionesConPlanificacion);

    if (error) {
      console.error('Error al guardar estimaciones de temas:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al guardar estimaciones de temas:', error);
    return false;
  }
}

/**
 * Elimina una planificación y todas sus estimaciones
 */
export async function eliminarPlanificacion(planificacionId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('planificacion_usuario')
      .delete()
      .eq('id', planificacionId);

    if (error) {
      console.error('Error al eliminar planificación:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar planificación:', error);
    return false;
  }
}
