import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/features/auth/services/authService';
import { PlanificacionUsuario, EstimacionTema } from '@/lib/supabase/supabaseClient';

/**
 * Verifica si el usuario tiene una planificación configurada
 */
export async function tienePlanificacionConfigurada(temarioId: string): Promise<boolean> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.log('No hay usuario autenticado o error:', authError);
      return false;
    }

    // Primero verificar que el temario existe y está activo
    const { data: temarioData, error: temarioError } = await supabase
      .from('temarios')
      .select('id, activo')
      .eq('id', temarioId)
      .eq('user_id', user.id)
      .eq('activo', true)
      .single();

    if (temarioError || !temarioData) {
      console.log('Temario no encontrado o inactivo:', temarioId);
      return false;
    }

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('id')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .eq('completado', true)
      .limit(1);

    if (error) {
      console.error('Error al verificar planificación:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error al verificar planificación:', error);
    return false;
  }
}

/**
 * Obtiene la planificación del usuario para un temario
 */
export async function obtenerPlanificacionUsuario(temarioId: string): Promise<PlanificacionUsuario | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return null;
    }

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('*')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No hay planificación
      }
      console.error('Error al obtener planificación:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener planificación:', error);
    return null;
  }
}

/**
 * Crea o actualiza la planificación del usuario
 */
export async function guardarPlanificacionUsuario(
  temarioId: string,
  planificacion: {
    tiempo_diario_promedio?: number;
    tiempo_por_dia?: Record<string, number>;
    fecha_examen?: string;
    fecha_examen_aproximada?: string;
    familiaridad_general?: number;
    preferencias_horario?: string[];
    frecuencia_repasos?: string;
  }
): Promise<string | null> {
  try {
    console.log('🔄 Guardando planificación:', { temarioId, planificacion });

    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.error('❌ No hay usuario autenticado:', authError);
      return null;
    }

    console.log('✅ Usuario autenticado:', user.id);

    // Verificar si ya existe una planificación
    const planificacionExistente = await obtenerPlanificacionUsuario(temarioId);
    console.log('📋 Planificación existente:', planificacionExistente?.id || 'No existe');

    const datosParaGuardar = {
      ...planificacion,
      completado: true,
      actualizado_en: new Date().toISOString()
    };

    if (planificacionExistente) {
      // Actualizar planificación existente
      console.log('🔄 Actualizando planificación existente con datos:', datosParaGuardar);

      const { data, error } = await supabase
        .from('planificacion_usuario')
        .update(datosParaGuardar)
        .eq('id', planificacionExistente.id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error al actualizar planificación:', error);
        console.error('❌ Detalles del error:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return null;
      }

      console.log('✅ Planificación actualizada exitosamente:', data.id);
      return data.id;
    } else {
      // Crear nueva planificación
      const datosParaCrear = {
        user_id: user.id,
        temario_id: temarioId,
        ...planificacion,
        completado: true
      };

      console.log('🆕 Creando nueva planificación con datos:', datosParaCrear);

      const { data, error } = await supabase
        .from('planificacion_usuario')
        .insert([datosParaCrear])
        .select()
        .single();

      if (error) {
        console.error('❌ Error al crear planificación:', error);
        console.error('❌ Detalles del error:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return null;
      }

      console.log('✅ Planificación creada exitosamente:', data.id);
      return data.id;
    }
  } catch (error) {
    console.error('❌ Error general al guardar planificación:', error);
    return null;
  }
}

/**
 * Obtiene las estimaciones de temas para una planificación
 */
export async function obtenerEstimacionesTemas(planificacionId: string): Promise<EstimacionTema[]> {
  try {
    const { data, error } = await supabase
      .from('estimaciones_temas')
      .select('*')
      .eq('planificacion_id', planificacionId);

    if (error) {
      console.error('Error al obtener estimaciones de temas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener estimaciones de temas:', error);
    return [];
  }
}

/**
 * Guarda las estimaciones de temas
 */
export async function guardarEstimacionesTemas(
  planificacionId: string,
  estimaciones: Array<{
    tema_id: string;
    horas_estimadas?: number;
    es_dificil: boolean;
    es_muy_importante: boolean;
    ya_dominado: boolean;
    notas?: string;
  }>
): Promise<boolean> {
  try {
    // Eliminar estimaciones existentes
    await supabase
      .from('estimaciones_temas')
      .delete()
      .eq('planificacion_id', planificacionId);

    // Insertar nuevas estimaciones
    const estimacionesConPlanificacion = estimaciones.map(est => ({
      ...est,
      planificacion_id: planificacionId
    }));

    const { error } = await supabase
      .from('estimaciones_temas')
      .insert(estimacionesConPlanificacion);

    if (error) {
      console.error('Error al guardar estimaciones de temas:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al guardar estimaciones de temas:', error);
    return false;
  }
}

/**
 * Elimina una planificación y todas sus estimaciones
 */
export async function eliminarPlanificacion(planificacionId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('planificacion_usuario')
      .delete()
      .eq('id', planificacionId);

    if (error) {
      console.error('Error al eliminar planificación:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar planificación:', error);
    return false;
  }
}
