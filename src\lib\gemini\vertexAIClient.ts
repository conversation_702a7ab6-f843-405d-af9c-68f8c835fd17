import { VertexAI, HarmCategory, HarmBlockThreshold } from '@google-cloud/vertexai';
import path from 'path';

// Configuración de Vertex AI
const PROJECT_ID = 'atomic-byway-461914-r0';
const LOCATION = 'us-central1'; // Región donde está disponible el modelo
const MODEL_NAME = 'gemini-2.5-flash-preview-05-20';

// Ruta al archivo de credenciales
const CREDENTIALS_PATH = path.join(process.cwd(), 'credentials', 'vertexai-access.json');

// Verificar que el archivo de credenciales existe
let vertexAI: VertexAI;
let model: any;

try {
  // Configurar las credenciales como variable de entorno
  process.env.GOOGLE_APPLICATION_CREDENTIALS = CREDENTIALS_PATH;
  
  // Inicializar Vertex AI
  vertexAI = new VertexAI({
    project: PROJECT_ID,
    location: LOCATION,
  });

  // Obtener el modelo generativo
  model = vertexAI.getGenerativeModel({
    model: MODEL_NAME,
    safetySettings: [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ],
  });

  console.log('✅ Vertex AI inicializado correctamente');
} catch (error) {
  console.error('❌ Error al inicializar Vertex AI:', error);
  throw new Error(`Error de configuración de Vertex AI: ${error}`);
}

export { vertexAI, model };

/**
 * Trunca el contenido de un documento si es demasiado largo
 */
export function truncarContenido(contenido: string | undefined | null, maxLength: number = 25000): string {
  // Verificar que el contenido sea una cadena válida
  if (contenido === undefined || contenido === null) {
    console.warn('Se intentó truncar un contenido undefined o null');
    return '';
  }

  // Asegurarse de que el contenido sea una cadena
  const contenidoStr = String(contenido);

  if (contenidoStr.length <= maxLength) {
    return contenidoStr;
  }

  return contenidoStr.substring(0, maxLength) +
    `\n\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;
}

// --- Lógica de Chunking (igual que en el cliente original) ---

const CHUNK_SIZE = 5000; // Caracteres
const CHUNK_OVERLAP = 200; // Caracteres
const MAX_TOTAL_CONTEXT_LENGTH = 50000; // Caracteres

interface DocumentChunk {
  originalDocumentTitle: string;
  chunkIndex: number;
  text: string;
}

function createTextChunks(
  documentTitle: string,
  content: string | undefined | null
): DocumentChunk[] {
  if (!content) {
    return [];
  }

  const contentStr = String(content);
  const chunks: DocumentChunk[] = [];
  
  for (let i = 0; i < contentStr.length; i += CHUNK_SIZE - CHUNK_OVERLAP) {
    const chunkText = contentStr.slice(i, i + CHUNK_SIZE);
    chunks.push({
      originalDocumentTitle: documentTitle,
      chunkIndex: chunks.length + 1,
      text: chunkText
    });
  }
  
  return chunks;
}

/**
 * Prepara los documentos para ser enviados a Vertex AI
 */
export function prepararDocumentos(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): string {
  if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
    console.warn('No se proporcionaron documentos válidos para preparar');
    return '';
  }

  // Crear chunks para todos los documentos
  const allChunks: DocumentChunk[] = [];
  
  documentos.forEach(doc => {
    if (doc && doc.titulo && doc.contenido) {
      const chunks = createTextChunks(doc.titulo, doc.contenido);
      allChunks.push(...chunks);
    }
  });

  if (allChunks.length === 0) {
    console.warn('No se pudieron crear chunks válidos de los documentos');
    return '';
  }

  let fullContext = allChunks.map(chunk => {
    return `
=== INICIO CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===
${chunk.text}
=== FIN CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===
`;
  }).join('\n\n');

  if (fullContext.length > MAX_TOTAL_CONTEXT_LENGTH) {
    console.warn(`El contexto combinado (${fullContext.length} caracteres) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`);
    fullContext = fullContext.substring(0, MAX_TOTAL_CONTEXT_LENGTH) +
      `\n\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;
  }
  
  return fullContext;
}
