/**
 * Cliente de Gemini - Migrado a OpenAI
 *
 * Este archivo actúa como un proxy para mantener la compatibilidad con el código existente
 * mientras usa OpenAI en lugar de Vertex AI.
 */

// Importar el cliente de OpenAI
import {
  openai,
  truncarContenido as truncarContenidoOpenAI,
  prepararDocumentos as prepararDocumentosOpenAI,
  llamarOpenAI
} from '../openai/openaiClient';

// Re-exportar las funciones para mantener compatibilidad con el código existente
export const model = {
  generateContent: async (prompt: string) => {
    // Adaptar la interfaz de Gemini a OpenAI
    const messages = [
      { role: 'user' as const, content: prompt }
    ];

    const respuesta = await llamarOpenAI(messages);

    return {
      response: {
        text: () => respuesta
      }
    };
  }
};

export const truncarContenido = truncarContenidoOpenAI;
export const prepararDocumentos = prepararDocumentosOpenAI;

// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado
// Todas las funciones de procesamiento de documentos y chunking ahora se manejan en openaiClient.ts
