/**
 * Cliente de Gemini - Migrado a Vertex AI
 *
 * Este archivo actúa como un proxy para mantener la compatibilidad con el código existente
 * mientras usa Vertex AI en lugar de la API directa de Google Generative AI.
 */

// Importar el cliente de Vertex AI
import {
  model,
  truncarContenido as truncarContenidoVertexAI,
  prepararDocumentos as prepararDocumentosVertexAI
} from './vertexAIClient';

// Re-exportar las funciones para mantener compatibilidad con el código existente
export { model };
export const truncarContenido = truncarContenidoVertexAI;
export const prepararDocumentos = prepararDocumentosVertexAI;

// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado
// Todas las funciones de procesamiento de documentos y chunking ahora se manejan en vertexAIClient.ts
