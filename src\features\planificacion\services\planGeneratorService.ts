import { llamarOpenAI } from '@/lib/openai/openaiClient';
import { PROMPT_PLAN_ESTUDIOS } from '@/config/prompts';
import { getOpenAIConfig } from '@/config/openai';
import { Tema, PlanificacionUsuario } from '@/lib/supabase/supabaseClient';
import { User } from '@supabase/supabase-js';

// Tipos para el plan estructurado
export interface TareaPlan {
  titulo: string;
  descripcion: string;
  tipo: 'estudio' | 'repaso' | 'practica' | 'evaluacion';
  duracionEstimada: string;
}

export interface DiaPlan {
  dia: string;
  horas: number;
  tareas: TareaPlan[];
}

export interface SemanaPlan {
  numero: number;
  fechaInicio: string;
  fechaFin: string;
  objetivoPrincipal: string;
  dias: DiaPlan[];
}

export interface ResumenPlan {
  tiempoTotalEstudio: string;
  numeroTemas: number;
  duracionEstudioNuevo: string;
  duracionRepasoFinal: string;
  [key: string]: any; // Permitir propiedades adicionales
}

export interface PlanEstudiosEstructurado {
  introduccion: string;
  resumen: ResumenPlan;
  semanas: SemanaPlan[];
  estrategiaRepasos: string | { titulo?: string; descripcion?: string };
  proximosPasos: string | { titulo?: string; descripcion?: string };
}

interface DatosPlanificacion {
  tiempo_diario_promedio?: number;
  tiempo_por_dia?: Record<string, number>;
  fecha_examen?: string;
  fecha_examen_aproximada?: string;
  familiaridad_general?: number;
  preferencias_horario?: string[];
  frecuencia_repasos?: string;
}

interface DatosTema {
  id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  horasEstimadas?: number;
  esDificil: boolean;
  esMuyImportante: boolean;
  yaDominado: boolean;
  notas?: string;
}

/**
 * Obtiene la planificación del usuario desde el servidor
 */
async function obtenerPlanificacionUsuarioServidor(temarioId: string, userId: string): Promise<PlanificacionUsuario | null> {
  try {
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    console.log('🔍 Consultando planificación con:', { userId, temarioId });

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('*')
      .eq('user_id', userId)
      .eq('temario_id', temarioId)
      .single();

    console.log('🔍 Resultado consulta planificación:', {
      data: data ? 'ENCONTRADA' : 'NO ENCONTRADA',
      error: error?.code,
      errorMessage: error?.message
    });

    if (error) {
      if (error.code === 'PGRST116') {
        console.log('❌ No hay planificación configurada (PGRST116)');
        return null;
      }
      console.error('❌ Error al obtener planificación:', error);
      return null;
    }

    console.log('✅ Planificación encontrada:', data.id);
    return data;
  } catch (error) {
    console.error('❌ Error al obtener planificación:', error);
    return null;
  }
}

/**
 * Obtiene los temas del temario desde el servidor
 */
async function obtenerTemasServidor(temarioId: string): Promise<Tema[]> {
  try {
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('temas')
      .select('*')
      .eq('temario_id', temarioId)
      .order('orden', { ascending: true });

    if (error) {
      console.error('Error al obtener temas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener temas:', error);
    return [];
  }
}



/**
 * Genera un plan de estudios personalizado usando IA
 */
export async function generarPlanEstudios(temarioId: string, user?: User): Promise<PlanEstudiosEstructurado> {
  try {
    // Si no se proporciona usuario, obtenerlo (para compatibilidad con llamadas desde el servidor)
    let usuarioActual = user;
    if (!usuarioActual) {
      const { createServerSupabaseClient } = await import('@/lib/supabase/server');
      const supabase = await createServerSupabaseClient();
      const { data: { user: serverUser }, error: authError } = await supabase.auth.getUser();

      if (!serverUser || authError) {
        throw new Error('No hay usuario autenticado para generar el plan de estudios');
      }
      usuarioActual = serverUser;
    }

    // Verificar que el temario existe
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    const { data: temarioData, error: temarioError } = await supabase
      .from('temarios')
      .select('id, titulo')
      .eq('id', temarioId)
      .eq('user_id', usuarioActual.id)
      .single();

    if (temarioError || !temarioData) {
      throw new Error(`El temario no existe o ha sido eliminado: ${temarioId}`);
    }

    // Obtener datos de planificación del usuario
    const planificacion = await obtenerPlanificacionUsuarioServidor(temarioId, usuarioActual.id);

    if (!planificacion) {
      throw new Error(`No se encontró planificación configurada para el temario: ${temarioData.titulo}`);
    }

    // Obtener temas del temario (sin estimaciones manuales - la IA las determinará automáticamente)
    const temas = await obtenerTemasServidor(temarioId);

    // Preparar datos de temas sin estimaciones manuales
    const temasParaIA: DatosTema[] = temas.map(tema => ({
      id: tema.id,
      numero: tema.numero,
      titulo: tema.titulo,
      descripcion: tema.descripcion,
      horasEstimadas: 0, // La IA determinará automáticamente las horas necesarias
      esDificil: false, // La IA determinará automáticamente la dificultad
      esMuyImportante: false, // La IA determinará automáticamente la importancia
      yaDominado: false, // La IA asume que todos los temas necesitan estudio
      notas: '' // Sin notas manuales
    }));

    // Preparar información del usuario para el prompt
    const informacionUsuario = prepararInformacionUsuario(planificacion, temasParaIA);

    // Construir el prompt final
    const promptFinal = PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);

    // Generar el plan con OpenAI
    const messages = [
      {
        role: 'system' as const,
        content: 'Eres un experto preparador de oposiciones. Tu tarea es crear planes de estudio detallados y personalizados en formato JSON válido.'
      },
      {
        role: 'user' as const,
        content: promptFinal
      }
    ];

    const responseText = await llamarOpenAI(messages, getOpenAIConfig('PLAN_ESTUDIOS'));

    if (!responseText || responseText.trim().length === 0) {
      throw new Error('La IA no generó ningún contenido para el plan de estudios');
    }

    // Parsear el JSON generado por la IA
    try {
      console.log('🔍 Respuesta completa de IA (primeros 1000 caracteres):', responseText.substring(0, 1000));

      // Intentar limpiar la respuesta si tiene texto adicional
      let jsonString = responseText.trim();

      // Si la respuesta contiene markdown, extraer solo el JSON
      if (jsonString.includes('```json')) {
        const jsonMatch = jsonString.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonString = jsonMatch[1].trim();
        }
      } else if (jsonString.includes('```')) {
        // Si tiene markdown sin especificar json
        const jsonMatch = jsonString.match(/```\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonString = jsonMatch[1].trim();
        }
      }

      // Si no empieza con {, buscar el primer {
      const startIndex = jsonString.indexOf('{');
      let endIndex = jsonString.lastIndexOf('}');

      if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
        jsonString = jsonString.substring(startIndex, endIndex + 1);
      }

      // Limpiar caracteres problemáticos básicos
      jsonString = jsonString
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Eliminar caracteres de control
        .replace(/\n\s*\n/g, '\n') // Eliminar líneas vacías múltiples
        .trim();

      console.log('🔍 JSON limpio (primeros 500 caracteres):', jsonString.substring(0, 500));
      console.log('🔍 JSON limpio (últimos 500 caracteres):', jsonString.substring(Math.max(0, jsonString.length - 500)));

      // Intentar reparar JSON truncado
      jsonString = repararJSONTruncado(jsonString);

      const planEstructurado: PlanEstudiosEstructurado = JSON.parse(jsonString);

      console.log('✅ Plan parseado exitosamente. Número de semanas:', planEstructurado.semanas?.length || 0);

      // Guardar el plan en la base de datos
      const { guardarPlanEstudiosServidor } = await import('./planEstudiosService');
      const planId = await guardarPlanEstudiosServidor(
        temarioId,
        planEstructurado,
        usuarioActual,
        `Plan de Estudios - ${new Date().toLocaleDateString()}`
      );

      if (planId) {
        console.log('✅ Plan de estudios guardado con ID:', planId);
      } else {
        console.warn('⚠️ No se pudo guardar el plan en la base de datos');
      }

      return planEstructurado;
    } catch (parseError) {
      console.error('❌ Error al parsear JSON del plan:', parseError);
      console.error('📄 Respuesta completa de IA:', responseText);

      // Intentar extraer información útil de la respuesta aunque no sea JSON válido
      let descripcionFallback = "Plan de estudios generado por IA";
      if (responseText && responseText.length > 100) {
        // Buscar si hay alguna introducción o descripción en el texto
        const lineas = responseText.split('\n').filter(linea => linea.trim().length > 0);
        if (lineas.length > 0) {
          descripcionFallback = lineas[0].substring(0, 200) + "...";
        }
      }

      // Fallback: crear un plan básico con el texto recibido
      const planFallback: PlanEstudiosEstructurado = {
        introduccion: `${descripcionFallback}\n\nNOTA: Hubo un problema al procesar la respuesta completa de la IA. Este es un plan básico de respaldo.`,
        resumen: {
          tiempoTotalEstudio: "Por determinar",
          numeroTemas: temas.length,
          duracionEstudioNuevo: "Por determinar",
          duracionRepasoFinal: "Por determinar"
        },
        semanas: [{
          numero: 1,
          fechaInicio: new Date().toISOString().split('T')[0],
          fechaFin: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 6 días después (domingo de la misma semana)
          objetivoPrincipal: "Comenzar el estudio del temario",
          dias: [{
            dia: "Lunes",
            horas: 4,
            tareas: [{
              titulo: "Revisar plan detallado",
              descripcion: "Por favor, regenera el plan para obtener una versión completa.",
              tipo: "estudio",
              duracionEstimada: "4h"
            }]
          }]
        }],
        estrategiaRepasos: "Regenera el plan para obtener la estrategia de repasos completa",
        proximosPasos: "Regenera el plan para obtener los próximos pasos detallados"
      };

      // Guardar el plan fallback también
      const { guardarPlanEstudiosServidor } = await import('./planEstudiosService');
      const planId = await guardarPlanEstudiosServidor(
        temarioId,
        planFallback,
        usuarioActual,
        `Plan de Estudios (Fallback) - ${new Date().toLocaleDateString()}`
      );

      if (planId) {
        console.log('✅ Plan fallback guardado con ID:', planId);
      }

      return planFallback;
    }
  } catch (error) {
    console.error('Error al generar plan de estudios:', error);
    throw error;
  }
}

/**
 * Prepara la información del usuario en formato legible para la IA
 */
function prepararInformacionUsuario(
  planificacion: DatosPlanificacion,
  temas: DatosTema[]
): string {
  let info = '';

  // Fecha actual para el plan
  const fechaActual = new Date();
  const fechaInicioFormatted = fechaActual.toISOString().split('T')[0];

  // Calcular duración del plan en semanas
  let semanasNecesarias = 26; // Por defecto 6 meses
  let fechaExamenCalculada = '';

  if (planificacion.fecha_examen) {
    const fechaExamen = new Date(planificacion.fecha_examen);
    const diferenciaDias = Math.ceil((fechaExamen.getTime() - fechaActual.getTime()) / (1000 * 60 * 60 * 24));
    semanasNecesarias = Math.max(1, Math.ceil(diferenciaDias / 7));
    fechaExamenCalculada = planificacion.fecha_examen;
  } else if (planificacion.fecha_examen_aproximada) {
    const fechaExamen = new Date(planificacion.fecha_examen_aproximada);
    const diferenciaDias = Math.ceil((fechaExamen.getTime() - fechaActual.getTime()) / (1000 * 60 * 60 * 24));
    semanasNecesarias = Math.max(1, Math.ceil(diferenciaDias / 7));
    fechaExamenCalculada = planificacion.fecha_examen_aproximada;
  }

  info += `**FECHA ACTUAL:** ${fechaInicioFormatted}\n`;
  info += `**DURACIÓN DEL PLAN:** ${semanasNecesarias} semanas (desde ${fechaInicioFormatted} hasta ${fechaExamenCalculada || 'fecha estimada'})\n`;
  info += `**CRÍTICO:** Debes generar EXACTAMENTE ${semanasNecesarias} semanas en el array "semanas". No menos.\n`;
  info += `**IMPORTANTE:** Todas las fechas del plan deben ser calculadas a partir de la fecha actual (${fechaInicioFormatted}).\n`;
  info += `- La Semana 1 debe comenzar el ${fechaInicioFormatted}\n`;
  info += `- Cada semana siguiente debe calcularse sumando 7 días a la anterior\n`;
  info += `- La última semana (Semana ${semanasNecesarias}) debe terminar cerca de la fecha del examen\n`;
  info += `- Usa el formato YYYY-MM-DD para todas las fechas\n\n`;

  // Disponibilidad de tiempo
  if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {
    info += '**Disponibilidad de Tiempo Diario:**\n';
    const dias = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
    dias.forEach(dia => {
      const horas = planificacion.tiempo_por_dia![dia];
      if (horas) {
        info += `- ${dia.charAt(0).toUpperCase() + dia.slice(1)}: ${horas}h\n`;
      }
    });
  } else if (planificacion.tiempo_diario_promedio) {
    info += `**Disponibilidad de Tiempo Diario:** Promedio ${planificacion.tiempo_diario_promedio}h/día\n`;
  }

  // Fecha del examen
  if (planificacion.fecha_examen) {
    info += `\n**Fecha del Examen:** ${planificacion.fecha_examen}\n`;
  } else if (planificacion.fecha_examen_aproximada) {
    info += `\n**Fecha del Examen (aproximada):** ${planificacion.fecha_examen_aproximada}\n`;
  }

  // Familiaridad general
  if (planificacion.familiaridad_general) {
    info += `\n**Familiaridad General con el Temario (1-5):** ${planificacion.familiaridad_general}\n`;
  }

  // Preferencias de horario
  if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {
    info += `\n**Preferencias de Horario:** ${planificacion.preferencias_horario.join(', ')}\n`;
  }

  // Frecuencia de repasos
  if (planificacion.frecuencia_repasos) {
    info += `\n**Frecuencia de Repasos Deseada:** ${planificacion.frecuencia_repasos}\n`;
  }

  // Índice del temario (sin estimaciones manuales - la IA las determinará automáticamente)
  info += '\n**Índice del Temario del Opositor:**\n';
  info += '**IMPORTANTE:** Como experto preparador, debes analizar automáticamente cada tema y determinar:\n';
  info += '- Horas de estudio necesarias según la complejidad y extensión aparente\n';
  info += '- Nivel de dificultad basado en el título y descripción\n';
  info += '- Importancia relativa dentro del conjunto del temario\n';
  info += '- Orden de estudio más eficiente\n\n';

  temas.forEach(tema => {
    info += `- **Tema ${tema.numero}: ${tema.titulo}**\n`;
    if (tema.descripcion) {
      info += `  - Descripción: ${tema.descripcion}\n`;
    }
    info += '\n';
  });

  return info;
}

/**
 * Intenta reparar un JSON truncado completando las estructuras incompletas
 */
function repararJSONTruncado(jsonString: string): string {
  try {
    // Primero intentar parsear tal como está
    JSON.parse(jsonString);
    return jsonString; // Si funciona, no necesita reparación
  } catch (error) {
    console.log('🔧 Intentando reparar JSON truncado...');

    let reparado = jsonString;

    // Contar llaves y corchetes para detectar estructuras incompletas
    const abreLlaves = (reparado.match(/\{/g) || []).length;
    const cierraLlaves = (reparado.match(/\}/g) || []).length;
    const abreCorchetes = (reparado.match(/\[/g) || []).length;
    const cierraCorchetes = (reparado.match(/\]/g) || []).length;

    console.log(`🔧 Llaves: ${abreLlaves} abiertas, ${cierraLlaves} cerradas`);
    console.log(`🔧 Corchetes: ${abreCorchetes} abiertos, ${cierraCorchetes} cerrados`);

    // Si hay una cadena incompleta al final, intentar cerrarla
    if (reparado.endsWith('"')) {
      reparado = reparado.slice(0, -1);
    }

    // Remover cualquier coma final antes de cerrar estructuras
    reparado = reparado.replace(/,\s*$/, '');

    // Cerrar arrays incompletos
    const corchetesFaltantes = abreCorchetes - cierraCorchetes;
    for (let i = 0; i < corchetesFaltantes; i++) {
      reparado += ']';
    }

    // Cerrar objetos incompletos
    const llavesFaltantes = abreLlaves - cierraLlaves;
    for (let i = 0; i < llavesFaltantes; i++) {
      reparado += '}';
    }

    console.log('🔧 JSON reparado (últimos 200 caracteres):', reparado.substring(Math.max(0, reparado.length - 200)));

    // Intentar parsear el JSON reparado
    try {
      JSON.parse(reparado);
      console.log('✅ JSON reparado exitosamente');
      return reparado;
    } catch (repairError) {
      console.log('❌ No se pudo reparar el JSON, usando fallback');
      throw repairError;
    }
  }
}
