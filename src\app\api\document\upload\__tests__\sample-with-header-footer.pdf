%PDF-1.0
1 0 obj <</Type /Catalog /Pages 2 0 R>> endobj
2 0 obj <</Type /Pages /Kids [3 0 R] /Count 1>> endobj
3 0 obj <</Type /Page /Parent 2 0 R /MediaBox [0 0 612 792]
/Contents 4 0 R >> endobj
4 0 obj <</Length 120>>
stream
BT
/F1 24 Tf
100 700 Td (Test Document Header Unique) Tj
100 400 Td (This is the main unique content of the PDF example.) Tj
100 100 Td (Page 1 Test Footer Unique) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f
0000000010 00000 n
0000000059 00000 n
0000000112 00000 n
0000000200 00000 n
trailer <</Size 5 /Root 1 0 R>>
%%EOF
