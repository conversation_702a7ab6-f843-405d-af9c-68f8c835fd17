import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lock, FiCalendar, FiTarget, FiBook, FiRefreshCw } from 'react-icons/fi';
import { PlanEstudiosEstructurado, TareaPlan } from '../services/planGeneratorService';
import {
  obtenerPlanEstudiosActivoCliente,
  obtenerProgresoPlaneCliente,
  guardarProgresoTareaCliente
} from '../services/planEstudiosClientService';
import { ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import { toast } from 'react-hot-toast';

interface PlanEstudiosViewerProps {
  plan: PlanEstudiosEstructurado;
  temarioId: string;
}

const PlanEstudiosViewer: React.FC<PlanEstudiosViewerProps> = ({ plan, temarioId }) => {
  const [progresoPlan, setProgresoPlan] = useState<ProgresoPlanEstudios[]>([]);
  const [planId, setPlanId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    cargarProgreso();
  }, [temarioId]);

  const cargarProgreso = async () => {
    try {
      // Obtener el plan activo
      const planActivo = await obtenerPlanEstudiosActivoCliente(temarioId);
      if (!planActivo) {
        console.warn('No se encontró plan activo para el temario:', temarioId);
        setPlanId(null);
        setProgresoPlan([]);
        setIsLoading(false);
        return;
      }

      setPlanId(planActivo.id);

      // Obtener el progreso del plan
      const progreso = await obtenerProgresoPlaneCliente(planActivo.id);
      setProgresoPlan(progreso);
    } catch (error) {
      console.error('Error al cargar progreso:', error);
      // Limpiar estado en caso de error
      setPlanId(null);
      setProgresoPlan([]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTareaCompletada = async (tarea: TareaPlan, semanaNum: number, dia: string) => {
    if (!planId) {
      toast.error('No se pudo identificar el plan de estudios');
      return;
    }

    try {
      // Verificar si la tarea ya está completada
      const tareaExistente = progresoPlan.find(p =>
        p.semana_numero === semanaNum &&
        p.dia_nombre === dia &&
        p.tarea_titulo === tarea.titulo
      );

      const nuevoEstado = !tareaExistente?.completado;

      // Guardar el progreso usando el nuevo servicio
      const exito = await guardarProgresoTareaCliente(
        planId,
        semanaNum,
        dia,
        tarea.titulo,
        tarea.tipo,
        nuevoEstado
      );

      if (exito) {
        // Actualizar el estado local
        setProgresoPlan(prev => {
          const index = prev.findIndex(p =>
            p.semana_numero === semanaNum &&
            p.dia_nombre === dia &&
            p.tarea_titulo === tarea.titulo
          );

          if (index >= 0) {
            // Actualizar existente
            const updated = [...prev];
            updated[index] = {
              ...updated[index],
              completado: nuevoEstado,
              fecha_completado: nuevoEstado ? new Date().toISOString() : undefined
            };
            return updated;
          } else {
            // Crear nuevo registro
            return [...prev, {
              id: `temp-${Date.now()}`, // ID temporal
              plan_id: planId,
              user_id: '', // Se llenará en el servidor
              semana_numero: semanaNum,
              dia_nombre: dia,
              tarea_titulo: tarea.titulo,
              tarea_tipo: tarea.tipo,
              completado: nuevoEstado,
              fecha_completado: nuevoEstado ? new Date().toISOString() : undefined,
              creado_en: new Date().toISOString(),
              actualizado_en: new Date().toISOString()
            }];
          }
        });

        toast.success(nuevoEstado ? 'Tarea completada' : 'Tarea marcada como pendiente');
      } else {
        toast.error('Error al actualizar el progreso');
      }
    } catch (error) {
      console.error('Error al actualizar tarea:', error);
      toast.error('Error al actualizar el progreso');
    }
  };

  const estaCompletada = (tarea: TareaPlan, semanaNum: number, dia: string): boolean => {
    return progresoPlan.some(p =>
      p.semana_numero === semanaNum &&
      p.dia_nombre === dia &&
      p.tarea_titulo === tarea.titulo &&
      p.completado
    );
  };

  const calcularProgreso = (): { completadas: number; total: number; porcentaje: number } => {
    // Validación defensiva
    if (!plan || !plan.semanas || !Array.isArray(plan.semanas)) {
      return { completadas: 0, total: 0, porcentaje: 0 };
    }

    const totalTareas = plan.semanas.reduce((acc, semana) => {
      if (!semana || !semana.dias || !Array.isArray(semana.dias)) {
        return acc;
      }
      return acc + semana.dias.reduce((dayAcc, dia) => {
        if (!dia || !dia.tareas || !Array.isArray(dia.tareas)) {
          return dayAcc;
        }
        return dayAcc + dia.tareas.length;
      }, 0);
    }, 0);

    const tareasCompletadasCount = progresoPlan.filter(p => p.completado).length;

    return {
      completadas: tareasCompletadasCount,
      total: totalTareas,
      porcentaje: totalTareas > 0 ? Math.round((tareasCompletadasCount / totalTareas) * 100) : 0
    };
  };

  const progreso = calcularProgreso();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Cargando progreso...</span>
      </div>
    );
  }

  // Validación defensiva para evitar errores de null/undefined
  if (!plan) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-gray-600">No se pudo cargar el plan de estudios</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Introducción */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 mb-2">Introducción</h3>
        <p className="text-blue-800">{plan.introduccion || 'Introducción no disponible'}</p>
      </div>

      {/* Progreso general */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Progreso General</h3>
          <span className="text-2xl font-bold text-green-600">{progreso.porcentaje}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
          <div 
            className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300"
            style={{ width: `${progreso.porcentaje}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600">
          {progreso.completadas} de {progreso.total} tareas completadas
        </p>
      </div>

      {/* Resumen */}
      {plan.resumen && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center">
              <FiClock className="w-5 h-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Tiempo Total</p>
                <p className="font-semibold">{plan.resumen.tiempoTotalEstudio || 'No disponible'}</p>
              </div>
            </div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center">
              <FiBook className="w-5 h-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Temas</p>
                <p className="font-semibold">{plan.resumen.numeroTemas || 'No disponible'}</p>
              </div>
            </div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center">
              <FiTarget className="w-5 h-5 text-purple-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Estudio Nuevo</p>
                <p className="font-semibold">{plan.resumen.duracionEstudioNuevo || 'No disponible'}</p>
              </div>
            </div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center">
              <FiRefreshCw className="w-5 h-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Repaso Final</p>
                <p className="font-semibold">{plan.resumen.duracionRepasoFinal || 'No disponible'}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cronograma semanal */}
      {plan.semanas && plan.semanas.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center">
            <FiCalendar className="w-5 h-5 mr-2" />
            Cronograma Semanal
          </h3>

          {plan.semanas.map((semana, semanaIndex) => (
            <div key={semanaIndex} className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-gray-900">
                  Semana {semana.numero}
                </h4>
                <span className="text-sm text-gray-600">
                  {semana.fechaInicio} - {semana.fechaFin}
                </span>
              </div>
              <p className="text-gray-700 mt-2">{semana.objetivoPrincipal}</p>
            </div>
            
            <div className="p-6 space-y-4">
              {semana.dias.map((dia, diaIndex) => (
                <div key={diaIndex} className="border border-gray-100 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-semibold text-gray-900">{dia.dia}</h5>
                    <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {dia.horas}h
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    {dia.tareas.map((tarea, tareaIndex) => {
                      const completada = estaCompletada(tarea, semana.numero, dia.dia);
                      return (
                        <div
                          key={tareaIndex}
                          className={`flex items-start p-3 rounded-lg border transition-all cursor-pointer ${
                            completada
                              ? 'bg-green-50 border-green-200'
                              : 'bg-white border-gray-200 hover:border-blue-300'
                          }`}
                          onClick={() => toggleTareaCompletada(tarea, semana.numero, dia.dia)}
                        >
                          <div className={`flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ${
                            completada 
                              ? 'bg-green-500 border-green-500' 
                              : 'border-gray-300 hover:border-blue-400'
                          }`}>
                            {completada && <FiCheck className="w-3 h-3 text-white" />}
                          </div>
                          
                          <div className="flex-1">
                            <h6 className={`font-medium ${completada ? 'text-green-800 line-through' : 'text-gray-900'}`}>
                              {tarea.titulo}
                            </h6>
                            {tarea.descripcion && (
                              <p className={`text-sm mt-1 ${completada ? 'text-green-700' : 'text-gray-600'}`}>
                                {tarea.descripcion}
                              </p>
                            )}
                            <div className="flex items-center mt-2 space-x-3">
                              <span className={`text-xs px-2 py-1 rounded ${
                                tarea.tipo === 'estudio' ? 'bg-blue-100 text-blue-800' :
                                tarea.tipo === 'repaso' ? 'bg-yellow-100 text-yellow-800' :
                                tarea.tipo === 'practica' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {tarea.tipo}
                              </span>
                              <span className="text-xs text-gray-500">
                                {tarea.duracionEstimada}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
            </div>
          ))}
        </div>
      )}

      {/* Estrategia de repasos */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold text-yellow-900 mb-2">Estrategia de Repasos</h3>
        <p className="text-yellow-800">
          {typeof plan.estrategiaRepasos === 'string'
            ? plan.estrategiaRepasos
            : plan.estrategiaRepasos.descripcion || 'Estrategia de repasos no disponible'
          }
        </p>
      </div>

      {/* Próximos pasos */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h3 className="font-semibold text-purple-900 mb-2">Próximos Pasos y Consejos</h3>
        <p className="text-purple-800">
          {typeof plan.proximosPasos === 'string'
            ? plan.proximosPasos
            : plan.proximosPasos.descripcion || 'Próximos pasos no disponibles'
          }
        </p>
      </div>
    </div>
  );
};

export default PlanEstudiosViewer;
