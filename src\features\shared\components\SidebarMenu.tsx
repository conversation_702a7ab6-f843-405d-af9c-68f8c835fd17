'use client';

import React, { useState } from 'react';
import {
  FiBook,
  FiFileText,
  FiCheckSquare,
  FiCalendar,
  FiMessageSquare,
  FiLayers,
  FiSettings,
  FiChevronDown,
  FiChevronRight,
  FiRefreshCw,
  FiPlus,
  FiList,
  FiCreditCard,
  FiTarget
} from 'react-icons/fi';

export type TabType = 'dashboard' | 'preguntas' | 'mapas' | 'flashcards' | 'misFlashcards' | 'tests' | 'misTests' | 'temario' | 'planEstudios' | 'gestionar';

interface MenuItem {
  id: TabType | string;
  label: string;
  icon: React.ReactNode;
  color: string;
  children?: MenuItem[];
  isGroup?: boolean;
}

interface SidebarMenuProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  children?: React.ReactNode;
}

const SidebarMenu: React.FC<SidebarMenuProps> = ({ activeTab, onTabChange, children }) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Principal',
      icon: <FiRefreshCw />,
      color: 'bg-gradient-to-r from-blue-600 to-purple-600'
    },
    {
      id: 'mi-temario-group',
      label: 'Mi Temario',
      icon: <FiBook />,
      color: 'bg-green-600',
      isGroup: true,
      children: [
        {
          id: 'temario',
          label: 'Mi Temario',
          icon: <FiBook />,
          color: 'bg-green-600'
        },
        {
          id: 'gestionar',
          label: 'Gestionar Documentos',
          icon: <FiSettings />,
          color: 'bg-gray-600'
        }
      ]
    },
    {
      id: 'planEstudios',
      label: 'Mi Plan de Estudios',
      icon: <FiCalendar />,
      color: 'bg-teal-600'
    },
    {
      id: 'preguntas',
      label: 'Habla con tu preparador',
      icon: <FiMessageSquare />,
      color: 'bg-blue-600'
    },
    {
      id: 'herramientas-group',
      label: 'Herramientas de estudio',
      icon: <FiTarget />,
      color: 'bg-purple-600',
      isGroup: true,
      children: [
        {
          id: 'flashcards-group',
          label: 'Flashcards',
          icon: <FiCreditCard />,
          color: 'bg-orange-500',
          isGroup: true,
          children: [
            {
              id: 'flashcards',
              label: 'Generador de Flashcards',
              icon: <FiPlus />,
              color: 'bg-orange-500'
            },
            {
              id: 'misFlashcards',
              label: 'Mis Flashcards',
              icon: <FiCreditCard />,
              color: 'bg-emerald-600'
            }
          ]
        },
        {
          id: 'tests-group',
          label: 'Tests',
          icon: <FiCheckSquare />,
          color: 'bg-indigo-600',
          isGroup: true,
          children: [
            {
              id: 'tests',
              label: 'Generador de Tests',
              icon: <FiPlus />,
              color: 'bg-indigo-600'
            },
            {
              id: 'misTests',
              label: 'Mis Tests',
              icon: <FiCheckSquare />,
              color: 'bg-pink-600'
            }
          ]
        }
      ]
    },
    {
      id: 'mapas',
      label: 'Mapas Mentales',
      icon: <FiLayers />,
      color: 'bg-purple-600'
    }
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isExpanded = (itemId: string) => expandedItems.includes(itemId);

  const collapseAllMenus = () => {
    setExpandedItems([]);
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = activeTab === item.id && !item.isGroup;
    const expanded = isExpanded(item.id as string);

    return (
      <div key={`${item.id}-${level}`}>
        <div
          className={`flex items-center justify-between px-${2 + level * 2} py-2 rounded-lg transition-all duration-200 cursor-pointer ${
            isActive && !hasChildren
              ? `text-white ${item.color} shadow-md`
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
          }`}
          onClick={() => {
            if (hasChildren || item.isGroup) {
              toggleExpanded(item.id as string);
            } else {
              collapseAllMenus();
              onTabChange(item.id as TabType);
            }
          }}
        >
          <div className="flex items-center">
            <span className="mr-2 text-sm">{item.icon}</span>
            <span className="text-sm font-medium">{item.label}</span>
          </div>
          {hasChildren && (
            <span className="text-xs">
              {expanded ? <FiChevronDown /> : <FiChevronRight />}
            </span>
          )}
        </div>

        {hasChildren && expanded && (
          <div className="ml-2 mt-1 space-y-1">
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-80 flex-shrink-0 space-y-4">
      <div className="bg-white rounded-xl shadow-sm p-4 sticky top-6">
        <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2">
          Menú de Estudio
        </h2>
        <nav className="space-y-1">
          {menuItems.map(item => renderMenuItem(item))}
        </nav>
      </div>
      {children}
    </div>
  );
};

export default SidebarMenu;
