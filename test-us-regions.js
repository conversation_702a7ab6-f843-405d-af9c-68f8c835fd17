// Script para probar la disponibilidad del modelo en regiones de Estados Unidos
const { VertexAI } = require('@google-cloud/vertexai');
const path = require('path');

// Configuración
const PROJECT_ID = 'atomic-byway-461914-r0';
const MODEL_NAME = 'gemini-2.5-flash-preview-05-20';

// Regiones principales de Estados Unidos
const US_REGIONS = [
  'us-central1',    // Iowa (región principal)
  'us-east1',       // Carolina del Sur
  'us-east4',       // Virginia del Norte
  'us-west1',       // Oregón
  'us-west2',       // Los Ángeles
  'us-west3',       // Salt Lake City
  'us-west4',       // Las Vegas
];

// Configurar credenciales
const CREDENTIALS_PATH = path.join(process.cwd(), 'credentials', 'vertexai-access.json');
process.env.GOOGLE_APPLICATION_CREDENTIALS = CREDENTIALS_PATH;

async function testRegion(region) {
  try {
    console.log(`🔍 Probando región: ${region}`);
    
    // Inicializar Vertex AI para esta región
    const vertexAI = new VertexAI({
      project: PROJECT_ID,
      location: region,
    });

    // Obtener el modelo
    const model = vertexAI.getGenerativeModel({
      model: MODEL_NAME,
    });

    // Hacer una prueba simple y rápida
    const result = await model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: 'Test' }]
      }]
    });

    // Si llegamos aquí, el modelo está disponible
    console.log(`✅ ${region}: DISPONIBLE`);
    return { region, available: true, error: null };
    
  } catch (error) {
    if (error.message.includes('404') || error.message.includes('NOT_FOUND')) {
      console.log(`❌ ${region}: NO DISPONIBLE (modelo no encontrado)`);
      return { region, available: false, error: 'MODEL_NOT_FOUND' };
    } else if (error.message.includes('403') || error.message.includes('PERMISSION_DENIED')) {
      console.log(`⚠️  ${region}: SIN PERMISOS`);
      return { region, available: false, error: 'PERMISSION_DENIED' };
    } else {
      console.log(`❓ ${region}: ERROR - ${error.message.substring(0, 100)}...`);
      return { region, available: false, error: 'UNKNOWN_ERROR' };
    }
  }
}

async function testUSRegions() {
  console.log('🇺🇸 Probando disponibilidad del modelo gemini-2.5-flash-preview-05-20 en regiones de EE.UU...\n');
  
  const results = [];
  
  // Probar cada región secuencialmente
  for (const region of US_REGIONS) {
    const result = await testRegion(region);
    results.push(result);
    
    // Pequeña pausa entre pruebas
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 RESUMEN DE RESULTADOS:');
  console.log('========================');
  
  const available = results.filter(r => r.available);
  const notFound = results.filter(r => r.error === 'MODEL_NOT_FOUND');
  
  if (available.length > 0) {
    console.log('\n✅ REGIONES DONDE ESTÁ DISPONIBLE:');
    available.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
    
    console.log('\n💡 RECOMENDACIÓN PARA EUROPA:');
    const bestForEurope = findBestForEurope(available.map(r => r.region));
    const regionInfo = getRegionInfo(bestForEurope);
    console.log(`   Usar: ${bestForEurope} (${regionInfo})`);
    console.log('   Razón: Menor latencia desde Europa y alta disponibilidad');
    
  } else {
    console.log('\n❌ NO DISPONIBLE en ninguna región de EE.UU.');
  }
  
  if (notFound.length > 0) {
    console.log('\n❌ REGIONES DONDE NO ESTÁ DISPONIBLE:');
    notFound.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
  }
}

function getRegionInfo(region) {
  const regionMap = {
    'us-central1': 'Iowa (región principal de Google)',
    'us-east1': 'Carolina del Sur',
    'us-east4': 'Virginia del Norte',
    'us-west1': 'Oregón',
    'us-west2': 'Los Ángeles, California',
    'us-west3': 'Salt Lake City, Utah',
    'us-west4': 'Las Vegas, Nevada'
  };
  return regionMap[region] || 'Ubicación desconocida';
}

function findBestForEurope(availableRegions) {
  // Orden de preferencia para acceso desde Europa
  // us-east4 y us-east1 están más cerca de Europa
  const preferenceOrder = [
    'us-east4',    // Virginia del Norte (más cerca de Europa)
    'us-east1',    // Carolina del Sur
    'us-central1', // Iowa (región principal, muy estable)
    'us-west1',    // Oregón
    'us-west2',    // Los Ángeles
    'us-west3',    // Salt Lake City
    'us-west4'     // Las Vegas
  ];
  
  for (const region of preferenceOrder) {
    if (availableRegions.includes(region)) {
      return region;
    }
  }
  
  return availableRegions[0]; // Fallback
}

// Ejecutar el test
testUSRegions().catch(console.error);
