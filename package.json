{"name": "oposiciones-ia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "clean": "node clean-cache.js", "fresh-start": "node clean-cache.js && npm run dev"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.7", "dompurify": "^3.2.6", "framer-motion": "^12.12.1", "next": "^15.1.8", "openai": "^5.1.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-select": "^5.8.0", "resend": "^4.5.1", "stripe": "^18.2.1", "validator": "^13.15.0", "zod": "^3.25.28"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.4", "typescript": "^5"}}