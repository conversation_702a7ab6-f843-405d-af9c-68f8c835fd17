# Guía de Migración: Stripe + Resend

## 📋 Paso 1: Variables de Entorno

Agrega estas variables a tu `.env.local`:

```env
# Resend Configuration
RESEND_API_KEY=re_BAyF3nde_Df67QTZmWa1eXjZ9dUkW5d8i
NOTIFICATION_EMAIL=<EMAIL>

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RWD1z07kFn3sIXhdMLMkzfOQdbmqC1sqdK4TUT5Ltr75arGtSam4ROsJf0dmnv8m9ZM1Y5MFcv4pDGaU9OoDC6t00yRyXcgtj
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RWD1z07kFn3sIXhB0Fpdp1titykyAjAQDi7fkkDbWBzLk4jgbmEl93t530dL0ItafvxNnH7wgOBqwPnSwrewYnk00aAQKZ4sw
STRIPE_WEBHOOK_SECRET=whsec_1RWD1z07kFn3sIXhtest_development_webhook_secret_placeholder

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Nota: Ahora usamos OpenAI en lugar de Vertex AI
# Configura OPENAI_API_KEY en tu .env.local
```

## 📦 Paso 2: Dependencias

Instala las dependencias necesarias:

```bash
npm install stripe resend react-hot-toast
```

## 📁 Paso 3: Estructura de Archivos a Crear

```
src/
├── lib/
│   └── stripe/
│       └── config.ts
├── app/
│   ├── api/
│   │   ├── stripe/
│   │   │   ├── create-checkout-session/
│   │   │   │   └── route.ts
│   │   │   └── webhook/
│   │   │       └── route.ts
│   │   └── notify-signup/
│   │       └── route.ts
│   ├── payment/
│   │   └── page.tsx
│   ├── thank-you/
│   │   └── page.tsx
│   └── landing/
│       └── page.tsx
└── components/
    └── ui/
        └── PlanCard.tsx
```

## 🔧 Paso 4: Actualizar Middleware

Si tienes middleware, agrega estas rutas públicas:

```typescript
const publicPaths = [
  // ... tus rutas existentes
  '/payment',
  '/thank-you',
  '/landing',
  '/api/notify-signup',
  '/api/stripe/create-checkout-session',
  '/api/stripe/webhook'
];
```

## 📁 Paso 5: Copiar Archivos

Copia estos archivos a tu aplicación:

1. **src/lib/stripe/config.ts** - Configuración de Stripe y planes
2. **src/app/api/stripe/create-checkout-session/route.ts** - API de checkout
3. **src/app/api/stripe/webhook/route.ts** - Webhook de Stripe
4. **src/app/api/notify-signup/route.ts** - API de notificaciones
5. **src/app/payment/page.tsx** - Página de pago
6. **src/app/thank-you/page.tsx** - Página de agradecimiento
7. **src/components/ui/PlanCard.tsx** - Componente de tarjeta de plan
8. **src/app/landing/page.tsx** - Landing page (opcional)

## 🔧 Paso 6: Configurar Toaster (Opcional)

Si quieres usar notificaciones toast, agrega a tu layout principal:

```typescript
import { Toaster } from 'react-hot-toast';

// En tu layout o componente principal
<Toaster
  position="top-right"
  toastOptions={{
    duration: 5000,
    style: {
      background: '#363636',
      color: '#fff',
    },
  }}
/>
```

## 🧪 Paso 7: Probar la Implementación

1. **Instala dependencias**: `npm install stripe resend react-hot-toast`
2. **Configura variables de entorno**
3. **Copia los archivos**
4. **Actualiza el middleware**
5. **Prueba las rutas**:
   - `/landing` - Landing page
   - `/payment?plan=free` - Plan gratuito
   - `/payment?plan=usuario` - Plan usuario
   - `/payment?plan=pro` - Plan pro

## 🔐 Paso 8: Configurar Webhook en Stripe (Producción)

Para producción, necesitarás:

1. Ir a https://dashboard.stripe.com/webhooks
2. Crear un nuevo endpoint: `https://tudominio.com/api/stripe/webhook`
3. Seleccionar eventos: `checkout.session.completed`, `payment_intent.succeeded`
4. Copiar el webhook secret y reemplazarlo en `.env.local`

## 📝 Notas Importantes

- **Seguridad**: Las claves de Stripe nunca deben exponerse en el frontend
- **Testing**: Usa las claves de test para desarrollo
- **Webhooks**: Son esenciales para confirmar pagos en producción
- **Emails**: Resend requiere verificación de dominio para producción
