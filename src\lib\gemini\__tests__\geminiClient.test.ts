import { prepararDocumentos } from '../geminiClient'; // Ajusta la ruta si es necesario

// Definir constantes (iguales a las de geminiClient.ts para consistencia)
const CHUNK_SIZE = 5000;
const CHUNK_OVERLAP = 200;
const MAX_TOTAL_CONTEXT_LENGTH = 50000;

// Definir un tipo para los documentos de prueba para claridad
interface TestDocument {
  titulo: string;
  contenido: string | null; // Permitir null para probar ese caso
  categoria?: string;
  numero_tema?: number;
}

describe('prepararDocumentos', () => {
  beforeEach(() => {
    // Limpiar mocks si los hubiera (no hay en este caso para prepararDocumentos directamente)
    // jest.clearAllMocks();
    // Restablecer el mock de console.warn si se quiere verificar sus llamadas
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks(); // Restaura los mocks de console
  });

  test('Documento Único, Sin Chunking Necesario (contenido < CHUNK_SIZE)', () => {
    const doc: TestDocument = { titulo: 'Doc Corto', contenido: 'Este es un contenido corto.' };
    const resultado = prepararDocumentos([doc]);

    expect(resultado).toContain('=== INICIO CHUNK: Doc Corto - Parte 1 ===');
    expect(resultado).toContain('Este es un contenido corto.');
    expect(resultado).toContain('=== FIN CHUNK: Doc Corto - Parte 1 ===');
    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(1); // Solo un chunk
    expect(console.warn).not.toHaveBeenCalled();
  });

  test('Documento Único, Múltiples Chunks (contenido > CHUNK_SIZE)', () => {
    const contenidoLargo = 'a'.repeat(CHUNK_SIZE + 100); // 5100 'a's
    const doc: TestDocument = { titulo: 'Doc Largo', contenido: contenidoLargo };
    const resultado = prepararDocumentos([doc]);

    expect(resultado).toContain('=== INICIO CHUNK: Doc Largo - Parte 1 ===');
    expect(resultado).toContain('a'.repeat(CHUNK_SIZE)); // Primer chunk completo
    expect(resultado).toContain('=== FIN CHUNK: Doc Largo - Parte 1 ===');

    expect(resultado).toContain('=== INICIO CHUNK: Doc Largo - Parte 2 ===');
    // El segundo chunk contendrá los 100 caracteres restantes + CHUNK_OVERLAP del anterior
    // Contenido del segundo chunk: overlap (200 'a's) + restantes (100 'a's) = 300 'a's
    // Pero la parte del overlap ya estaba en el chunk anterior, el texto único del chunk 2 son los 100 'a's finales
    // El texto del chunk 2 será: content.substring(CHUNK_SIZE - CHUNK_OVERLAP, CHUNK_SIZE + 100)
    // es decir, content.substring(4800, 5100)
    const expectedTextInChunk2 = 'a'.repeat(100 + CHUNK_OVERLAP); // 300 'a's
    expect(resultado).toContain(expectedTextInChunk2);
    expect(resultado).toContain('=== FIN CHUNK: Doc Largo - Parte 2 ===');

    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(2); // Dos chunks
  });

  test('Múltiples Documentos, Múltiples Chunks', () => {
    const docs: TestDocument[] = [
      { titulo: 'Doc 1', contenido: 'Contenido del Doc 1.' },
      { titulo: 'Doc 2', contenido: 'b'.repeat(CHUNK_SIZE + 50) }, // Generará 2 chunks
    ];
    const resultado = prepararDocumentos(docs);

    expect(resultado).toContain('=== INICIO CHUNK: Doc 1 - Parte 1 ===');
    expect(resultado).toContain('Contenido del Doc 1.');
    expect(resultado).toContain('=== FIN CHUNK: Doc 1 - Parte 1 ===');

    expect(resultado).toContain('=== INICIO CHUNK: Doc 2 - Parte 1 ===');
    expect(resultado).toContain('b'.repeat(CHUNK_SIZE));
    expect(resultado).toContain('=== FIN CHUNK: Doc 2 - Parte 1 ===');

    expect(resultado).toContain('=== INICIO CHUNK: Doc 2 - Parte 2 ===');
    expect(resultado).toContain('b'.repeat(50 + CHUNK_OVERLAP)); // 250 'b's
    expect(resultado).toContain('=== FIN CHUNK: Doc 2 - Parte 2 ===');

    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(3); // 1 del Doc1 + 2 del Doc2
  });

  test('Documento con Contenido Vacío', () => {
    const doc: TestDocument = { titulo: 'Doc Vacío', contenido: '' };
    const resultado = prepararDocumentos([doc]);
    // Esperamos que no se genere ningún chunk para un documento con contenido vacío
    // La función createTextChunks devuelve [] y prepararDocumentos debería devolver '' o manejarlo.
    // Según la implementación actual, si no hay chunks, devuelve ''.
    expect(resultado).toBe('');
    expect(console.warn).toHaveBeenCalledWith('No se generaron chunks a partir de los documentos proporcionados.');
  });

  test('Documento con Contenido Nulo', () => {
    const doc: TestDocument = { titulo: 'Doc Nulo', contenido: null };
    const resultado = prepararDocumentos([doc]);
     // La función createTextChunks devuelve [] y prepararDocumentos debería devolver '' o manejarlo.
    expect(resultado).toBe('');
     // El warning de "Documento inválido..." se mostraría si el título o contenido son undefined,
     // pero createTextChunks maneja null y devuelve []. Luego, si allChunks está vacío, otro warning.
    expect(console.warn).toHaveBeenCalledWith('Documento inválido, sin título o contenido en prepararDocumentos:', expect.objectContaining({ titulo: 'Doc Nulo', contenido: null }));
    expect(console.warn).toHaveBeenCalledWith('No se generaron chunks a partir de los documentos proporcionados.');

  });

  test('Documento con título, categoría y número de tema', () => {
    const doc: TestDocument = {
      titulo: 'Título Principal',
      contenido: 'Contenido relevante.',
      categoria: 'Legislación',
      numero_tema: 5
    };
    const resultado = prepararDocumentos([doc]);
    const expectedChunkTitle = '[Legislación] Tema 5: Título Principal';

    expect(resultado).toContain(`=== INICIO CHUNK: ${expectedChunkTitle} - Parte 1 ===`);
    expect(resultado).toContain('Contenido relevante.');
    expect(resultado).toContain(`=== FIN CHUNK: ${expectedChunkTitle} - Parte 1 ===`);
  });


  test('Lista de Documentos Vacía', () => {
    const resultado = prepararDocumentos([]);
    expect(resultado).toBe('');
    expect(console.warn).toHaveBeenCalledWith('No se proporcionaron documentos válidos para prepararDocumentos');
  });

  test('Verificación del Solapamiento (Overlap)', () => {
    const commonPart = 'x'.repeat(CHUNK_OVERLAP); // 200 'x's
    const firstPartUnique = 'a'.repeat(CHUNK_SIZE - CHUNK_OVERLAP); // 4800 'a's
    const secondPartUnique = 'b'.repeat(100); // 100 'b's

    // Contenido total: firstPartUnique + commonPart + secondPartUnique
    // = 4800 'a's + 200 'x's + 100 'b's = 5100 caracteres
    const contenidoConSolapamiento = firstPartUnique + commonPart + secondPartUnique;
    const doc: TestDocument = { titulo: 'Doc Solapamiento', contenido: contenidoConSolapamiento };
    const resultado = prepararDocumentos([doc]);

    // Chunk 1: firstPartUnique + commonPart (4800 'a's + 200 'x's)
    const chunk1ExpectedText = firstPartUnique + commonPart;
    // Chunk 2: commonPart + secondPartUnique (200 'x's + 100 'b's)
    const chunk2ExpectedText = commonPart + secondPartUnique;

    expect(resultado).toContain(`=== INICIO CHUNK: Doc Solapamiento - Parte 1 ===\n${chunk1ExpectedText}\n=== FIN CHUNK: Doc Solapamiento - Parte 1 ===`);
    expect(resultado).toContain(`=== INICIO CHUNK: Doc Solapamiento - Parte 2 ===\n${chunk2ExpectedText}\n=== FIN CHUNK: Doc Solapamiento - Parte 2 ===`);

    // Verificar que el final del texto del chunk 1 (commonPart) es el inicio del texto del chunk 2 (commonPart)
    const chunk1Text = resultado.substring(
      resultado.indexOf('=== INICIO CHUNK: Doc Solapamiento - Parte 1 ===') + `=== INICIO CHUNK: Doc Solapamiento - Parte 1 ===\n`.length,
      resultado.indexOf('\n=== FIN CHUNK: Doc Solapamiento - Parte 1 ===')
    );
    const chunk2Text = resultado.substring(
      resultado.indexOf('=== INICIO CHUNK: Doc Solapamiento - Parte 2 ===') + `=== INICIO CHUNK: Doc Solapamiento - Parte 2 ===\n`.length,
      resultado.indexOf('\n=== FIN CHUNK: Doc Solapamiento - Parte 2 ===')
    );

    expect(chunk1Text.substring(CHUNK_SIZE - CHUNK_OVERLAP)).toBe(commonPart);
    expect(chunk2Text.substring(0, CHUNK_OVERLAP)).toBe(commonPart);
  });

  test('Verificación del Formato del Chunk', () => {
    const doc: TestDocument = { titulo: 'Doc Formato', contenido: 'Texto de prueba.', categoria: 'TestCat', numero_tema: 101 };
    const resultado = prepararDocumentos([doc]);
    const expectedFormattedTitle = '[TestCat] Tema 101: Doc Formato';

    expect(resultado).toMatch(new RegExp(`=== INICIO CHUNK: ${expectedFormattedTitle.replace(/[\[\]]/g, '\\$&')} - Parte 1 ===`));
    expect(resultado).toContain('Texto de prueba.');
    expect(resultado).toMatch(new RegExp(`=== FIN CHUNK: ${expectedFormattedTitle.replace(/[\[\]]/g, '\\$&')} - Parte 1 ===`));
  });

  test('Truncamiento Global del Contexto Total (MAX_TOTAL_CONTEXT_LENGTH)', () => {
    // Crear contenido que definitivamente excederá MAX_TOTAL_CONTEXT_LENGTH
    // MAX_TOTAL_CONTEXT_LENGTH es 50000. Un chunk es ~5000. 10 chunks son ~50000.
    // Necesitamos más de 10 chunks. Cada chunk añade ~100 caracteres de formato.
    // Si tenemos 11 chunks, son 11 * 5000 = 55000 de texto puro.
    // Más el formato: 11 * (~100 chars de formato) = ~1100. Total ~56100.
    const numCharsPerDoc = CHUNK_SIZE * 2; // Cada doc genera 2 chunks
    const numDocs = 6; // 6 docs * 2 chunks/doc = 12 chunks.
    const singleDocContent = 'c'.repeat(numCharsPerDoc);
    const docs: TestDocument[] = [];
    for (let i = 0; i < numDocs; i++) {
      docs.push({ titulo: `Doc Masivo ${i + 1}`, contenido: singleDocContent });
    }

    const resultado = prepararDocumentos(docs);

    expect(resultado.length).toBeLessThanOrEqual(MAX_TOTAL_CONTEXT_LENGTH + "\n\n[CONTEXTO GENERAL TRUNCADO: ...]".length + 100); // Un pequeño margen
    expect(resultado).toContain(`\n\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`);
    expect(console.warn).toHaveBeenCalledWith(expect.stringContaining(`El contexto combinado (${(CHUNK_SIZE + "=== INICIO CHUNK: Doc Masivo 1 - Parte 1 ===\n\n=== FIN CHUNK: Doc Masivo 1 - Parte 1 ===\n\n".length - CHUNK_OVERLAP)*12 - "\n\n".length}) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`));
  });

  test('Caso Límite: Contenido exactamente CHUNK_SIZE', () => {
    const contenido = 'd'.repeat(CHUNK_SIZE);
    const doc: TestDocument = { titulo: 'Doc Límite Exacto', contenido };
    const resultado = prepararDocumentos([doc]);

    expect(resultado).toContain('=== INICIO CHUNK: Doc Límite Exacto - Parte 1 ===');
    expect(resultado).toContain(contenido);
    expect(resultado).toContain('=== FIN CHUNK: Doc Límite Exacto - Parte 1 ===');
    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(1); // Solo un chunk
  });

  test('Caso Límite: Contenido CHUNK_SIZE + 1', () => {
    const contenido = 'e'.repeat(CHUNK_SIZE + 1);
    const doc: TestDocument = { titulo: 'Doc Límite Más Uno', contenido };
    const resultado = prepararDocumentos([doc]);

    expect(resultado).toContain('=== INICIO CHUNK: Doc Límite Más Uno - Parte 1 ===');
    expect(resultado).toContain('e'.repeat(CHUNK_SIZE));
    expect(resultado).toContain('=== FIN CHUNK: Doc Límite Más Uno - Parte 1 ===');

    expect(resultado).toContain('=== INICIO CHUNK: Doc Límite Más Uno - Parte 2 ===');
    // El segundo chunk contendrá el carácter restante + CHUNK_OVERLAP del anterior
    expect(resultado).toContain('e'.repeat(1 + CHUNK_OVERLAP));
    expect(resultado).toContain('=== FIN CHUNK: Doc Límite Más Uno - Parte 2 ===');
    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(2);
  });

  test('Caso Límite: Contenido CHUNK_SIZE - 1', () => {
    const contenido = 'f'.repeat(CHUNK_SIZE - 1);
    const doc: TestDocument = { titulo: 'Doc Límite Menos Uno', contenido };
    const resultado = prepararDocumentos([doc]);

    expect(resultado).toContain('=== INICIO CHUNK: Doc Límite Menos Uno - Parte 1 ===');
    expect(resultado).toContain(contenido);
    expect(resultado).toContain('=== FIN CHUNK: Doc Límite Menos Uno - Parte 1 ===');
    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(1); // Solo un chunk
  });

  test('Asegurar que createTextChunks maneja correctamente el overlap para no perder contenido al final', () => {
    // Este test es más para la lógica interna de createTextChunks, pero lo probamos vía prepararDocumentos
    // Si CHUNK_SIZE = 100, OVERLAP = 20
    // Contenido de 185 caracteres.
    // Chunk 1: 0-100
    // Próximo currentIndex = 100 - 20 = 80
    // Chunk 2: 80-180 (100 chars)
    // Próximo currentIndex = 80 + 100 - 20 = 160
    // Chunk 3: 160-185 (25 chars) -> este es el importante
    const localChunkSize = 100;
    const localOverlap = 20;
    const localMaxContext = 1000;

    // Sobrescribir constantes para este test específico si es necesario, o crear una función de test parametrizable
    // Para este ejemplo, asumiré que CHUNK_SIZE y CHUNK_OVERLAP son los globales.
    // Adaptar el contenido al CHUNK_SIZE y CHUNK_OVERLAP globales.
    // CHUNK_SIZE = 5000, CHUNK_OVERLAP = 200
    // Contenido: (CHUNK_SIZE - CHUNK_OVERLAP) * 2 + (CHUNK_OVERLAP + 50)
    // = 4800 * 2 + 250 = 9600 + 250 = 9850
    // Chunk 1: 0 - 5000 (texto 'a')
    // next index: 4800
    // Chunk 2: 4800 - 9800 (texto 'b')
    // next index: 4800 + 5000 - 200 = 9600
    // Chunk 3: 9600 - 9850 (texto 'c', 250 caracteres)

    const part1 = 'a'.repeat(CHUNK_SIZE); // 5000
    const part2 = 'b'.repeat(CHUNK_SIZE - CHUNK_OVERLAP); // 4800
    const part3 = 'c'.repeat(50 + CHUNK_OVERLAP); // 250
    const contenido = part1.substring(0, CHUNK_SIZE - CHUNK_OVERLAP) + // 4800 'a's
                      part2.substring(0, CHUNK_SIZE - CHUNK_OVERLAP) + // 4800 'b's (total 9600)
                      part3; // 250 'c's (total 9850)

    // Esperamos 3 chunks.
    // Chunk 1: primeros 4800 'a's + primeros 200 'b's
    // Chunk 2: últimos 200 'a's (del overlap) + 4800 'b's + primeros 200 'c's (del overlap)
    // Chunk 3: últimos 200 'b's (del overlap) + 250 'c's

    const doc: TestDocument = { titulo: 'Doc Overlap Final', contenido };
    const resultado = prepararDocumentos([doc]);

    // Chunk 1: Debería ser los primeros CHUNK_SIZE caracteres del contenido.
    const expectedChunk1Text = contenido.substring(0, CHUNK_SIZE);
    expect(resultado).toContain(`=== INICIO CHUNK: Doc Overlap Final - Parte 1 ===\n${expectedChunk1Text}\n=== FIN CHUNK: Doc Overlap Final - Parte 1 ===`);

    // Chunk 2: Debería ser CHUNK_OVERLAP del final de chunk1 + los siguientes (CHUNK_SIZE - CHUNK_OVERLAP) caracteres.
    // Su texto comienza en contenido[CHUNK_SIZE - CHUNK_OVERLAP] y tiene CHUNK_SIZE caracteres.
    const expectedChunk2Text = contenido.substring(CHUNK_SIZE - CHUNK_OVERLAP, CHUNK_SIZE - CHUNK_OVERLAP + CHUNK_SIZE);
    expect(resultado).toContain(`=== INICIO CHUNK: Doc Overlap Final - Parte 2 ===\n${expectedChunk2Text}\n=== FIN CHUNK: Doc Overlap Final - Parte 2 ===`);

    // Chunk 3: El resto. Comienza en contenido[2 * (CHUNK_SIZE - CHUNK_OVERLAP)]
    const expectedChunk3Text = contenido.substring(2 * (CHUNK_SIZE - CHUNK_OVERLAP));
    expect(resultado).toContain(`=== INICIO CHUNK: Doc Overlap Final - Parte 3 ===\n${expectedChunk3Text}\n=== FIN CHUNK: Doc Overlap Final - Parte 3 ===`);

    expect(resultado.match(/=== INICIO CHUNK/g) || []).toHaveLength(3);
    expect(resultado).not.toContain("[CONTEXTO GENERAL TRUNCADO");
  });

});

// Helper para generar texto largo para pruebas de truncamiento global
function generarTextoExcesivo(numChunksParaExceder: number): TestDocument[] {
  const docs: TestDocument[] = [];
  // Cada chunk de texto es CHUNK_SIZE. El formato añade approx 100 chars por chunk.
  // (CHUNK_SIZE + 100 chars de formato) * numChunksParaExceder > MAX_TOTAL_CONTEXT_LENGTH
  const contenidoPorDocumento = 'z'.repeat(CHUNK_SIZE); // Cada documento es un chunk completo de texto

  // Calculamos cuántos documentos (y por tanto chunks de texto) necesitamos para exceder.
  // El formato es: "\n\n=== INICIO CHUNK: ${title} - Parte ${idx} ===\n${text}\n=== FIN CHUNK: ${title} - Parte ${idx} ==="
  // Longitud aprox del formato: 4 (separadores) + 30 (inicio) + len(titulo) + 8 (parte) + 30 (fin) ~ 72 + len(titulo)
  // Digamos que el título es "Doc Excesivo XX", unos 15 chars. Total formato ~87.
  // Más el texto del chunk: CHUNK_SIZE. Total por chunk: CHUNK_SIZE + 87.
  // Separador entre chunks: "\n\n" (2 chars).
  // (CHUNK_SIZE + 87 + 2) * N > MAX_TOTAL_CONTEXT_LENGTH
  // N > MAX_TOTAL_CONTEXT_LENGTH / (CHUNK_SIZE + 89)
  // N > 50000 / (5000 + 89) = 50000 / 5089 ~ 9.82. Así que 10 chunks deberían ser suficientes.
  // Si cada doc es un chunk, 10 documentos.

  // Ajustar numChunksParaExceder si el cálculo anterior es muy justo.
  // Para estar seguros, usamos el numChunksParaExceder pasado.
  for (let i = 0; i < numChunksParaExceder; i++) {
    docs.push({ titulo: `Doc Excesivo ${i + 1}`, contenido: contenidoPorDocumento });
  }
  return docs;
}

describe('prepararDocumentos - Truncamiento Global Detallado', () => {
   beforeEach(() => {
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Truncamiento Global del Contexto Total - verificación de longitud exacta', () => {
    const numChunks = Math.ceil(MAX_TOTAL_CONTEXT_LENGTH / (CHUNK_SIZE - CHUNK_OVERLAP)) + 2; // Asegurar exceso
    const docs = generarTextoExcesivo(numChunks); // Genera N documentos, cada uno será un chunk de texto

    const resultado = prepararDocumentos(docs);
    const expectedSuffix = `\n\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;

    expect(resultado.length).toBe(MAX_TOTAL_CONTEXT_LENGTH + expectedSuffix.length);
    expect(resultado.endsWith(expectedSuffix)).toBe(true);
    expect(console.warn).toHaveBeenCalledWith(expect.stringContaining(`El contexto combinado`));
    expect(console.warn).toHaveBeenCalledWith(expect.stringContaining(`excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`));
  });
});
