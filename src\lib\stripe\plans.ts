// src/lib/stripe/plans.ts
// Configuración de planes (solo datos, sin instancia de Stripe)

export const PLANS = {
  free: {
    id: 'free',
    name: '<PERSON> Gratis',
    price: 0,
    stripeProductId: null,
    stripePriceId: null,
    features: [
      'Sube 1 documento',
      '5 mapas mentales a la semana',
      '30 preguntas tipo test a la semana',
      '30 flashcards a la semana',
    ],
    limits: {
      documents: 1,
      mindMapsPerWeek: 5,
      testsPerWeek: 30,
      flashcardsPerWeek: 30,
    }
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 999, // En centavos (€9.99)
    stripeProductId: 'prod_SR65BdKdek1OXd',
    stripePriceId: 'price_1RWE1807kFn3sIXhlnKAfLoV',
    features: [
      'Sube 5 documentos',
      '10 mapas mentales a la semana',
      '150 preguntas tipo test a la semana',
      '150 flashcards a la semana',
    ],
    limits: {
      documents: 5,
      mindMapsPerWeek: 10,
      testsPerWeek: 150,
      flashcardsPerWeek: 150,
    }
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 2000, // En centavos (€20.00)
    stripeProductId: 'prod_SR66U2G7bVJqu3',
    stripePriceId: 'price_1RWE7l07kFn3sIXh1rCiXMqp',
    features: [
      'Sube un temario completo',
      'Planificación de estudio hasta examen mediante IA',
      'Mapas mentales ilimitados',
      'Preguntas tipo test ilimitadas',
      'Flashcards ilimitadas',
    ],
    limits: {
      documents: -1, // Ilimitado
      mindMapsPerWeek: -1, // Ilimitado
      testsPerWeek: -1, // Ilimitado
      flashcardsPerWeek: -1, // Ilimitado
    }
  }
} as const;

export type PlanId = keyof typeof PLANS;

// Función para obtener plan por ID
export function getPlanById(planId: string): typeof PLANS[PlanId] | null {
  return PLANS[planId as PlanId] || null;
}

// Función para validar si un plan es válido
export function isValidPlan(planId: string): planId is PlanId {
  return planId in PLANS;
}

// URLs de la aplicación
export const APP_URLS = {
  success: `${process.env.NEXT_PUBLIC_APP_URL}/thank-you`,
  cancel: `${process.env.NEXT_PUBLIC_APP_URL}/payment`,
  webhook: `${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/webhook`,
} as const;
