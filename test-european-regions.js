// Script para probar la disponibilidad del modelo en diferentes regiones europeas
const { VertexAI } = require('@google-cloud/vertexai');
const path = require('path');

// Configuración
const PROJECT_ID = 'atomic-byway-461914-r0';
const MODEL_NAME = 'gemini-2.5-flash-preview-05-20';

// Regiones europeas de Google Cloud
const EUROPEAN_REGIONS = [
  'europe-west1',     // Bélgica
  'europe-west2',     // Londres
  'europe-west3',     // Frankfurt
  'europe-west4',     // Países Bajos
  'europe-west6',     // Zurich
  'europe-west8',     // <PERSON><PERSON><PERSON>
  'europe-west9',     // <PERSON>r<PERSON>
  'europe-west10',    // Be<PERSON><PERSON>
  'europe-west12',    // <PERSON><PERSON><PERSON>
  'europe-north1',    // Finlandia
  'europe-central2',  // Varsovia
  'europe-southwest1' // Madrid
];

// Configurar credenciales
const CREDENTIALS_PATH = path.join(process.cwd(), 'credentials', 'vertexai-access.json');
process.env.GOOGLE_APPLICATION_CREDENTIALS = CREDENTIALS_PATH;

async function testRegion(region) {
  try {
    console.log(`🔍 Probando región: ${region}`);
    
    // Inicializar Vertex AI para esta región
    const vertexAI = new VertexAI({
      project: PROJECT_ID,
      location: region,
    });

    // Obtener el modelo
    const model = vertexAI.getGenerativeModel({
      model: MODEL_NAME,
    });

    // Hacer una prueba simple y rápida
    const result = await model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: 'Test' }]
      }]
    });

    // Si llegamos aquí, el modelo está disponible
    const response = result.response;
    console.log(`✅ ${region}: DISPONIBLE`);
    return { region, available: true, error: null };
    
  } catch (error) {
    if (error.message.includes('404') || error.message.includes('NOT_FOUND')) {
      console.log(`❌ ${region}: NO DISPONIBLE (modelo no encontrado)`);
      return { region, available: false, error: 'MODEL_NOT_FOUND' };
    } else if (error.message.includes('403') || error.message.includes('PERMISSION_DENIED')) {
      console.log(`⚠️  ${region}: SIN PERMISOS (región podría estar disponible pero sin acceso)`);
      return { region, available: false, error: 'PERMISSION_DENIED' };
    } else {
      console.log(`❓ ${region}: ERROR DESCONOCIDO - ${error.message.substring(0, 100)}...`);
      return { region, available: false, error: 'UNKNOWN_ERROR' };
    }
  }
}

async function testAllEuropeanRegions() {
  console.log('🌍 Probando disponibilidad del modelo gemini-2.5-flash-preview-05-20 en regiones europeas...\n');
  
  const results = [];
  
  // Probar cada región secuencialmente para evitar rate limits
  for (const region of EUROPEAN_REGIONS) {
    const result = await testRegion(region);
    results.push(result);
    
    // Pequeña pausa entre pruebas
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 RESUMEN DE RESULTADOS:');
  console.log('========================');
  
  const available = results.filter(r => r.available);
  const notFound = results.filter(r => r.error === 'MODEL_NOT_FOUND');
  const permissionDenied = results.filter(r => r.error === 'PERMISSION_DENIED');
  const unknownError = results.filter(r => r.error === 'UNKNOWN_ERROR');
  
  if (available.length > 0) {
    console.log('\n✅ REGIONES DONDE ESTÁ DISPONIBLE:');
    available.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
  } else {
    console.log('\n❌ NO DISPONIBLE en ninguna región europea');
  }
  
  if (notFound.length > 0) {
    console.log('\n❌ REGIONES DONDE NO ESTÁ DISPONIBLE:');
    notFound.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
  }
  
  if (permissionDenied.length > 0) {
    console.log('\n⚠️  REGIONES SIN PERMISOS (podrían estar disponibles):');
    permissionDenied.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
  }
  
  if (unknownError.length > 0) {
    console.log('\n❓ REGIONES CON ERRORES DESCONOCIDOS:');
    unknownError.forEach(r => {
      const regionInfo = getRegionInfo(r.region);
      console.log(`   • ${r.region} (${regionInfo})`);
    });
  }
  
  console.log('\n💡 RECOMENDACIÓN:');
  if (available.length > 0) {
    const closest = findClosestToFrance(available.map(r => r.region));
    const regionInfo = getRegionInfo(closest);
    console.log(`   Usar: ${closest} (${regionInfo}) - Más cercana a París`);
  } else {
    console.log('   Mantener us-central1 ya que el modelo no está disponible en Europa');
  }
}

function getRegionInfo(region) {
  const regionMap = {
    'europe-west1': 'Bélgica',
    'europe-west2': 'Londres, Reino Unido',
    'europe-west3': 'Frankfurt, Alemania',
    'europe-west4': 'Países Bajos',
    'europe-west6': 'Zurich, Suiza',
    'europe-west8': 'Milán, Italia',
    'europe-west9': 'París, Francia',
    'europe-west10': 'Berlín, Alemania',
    'europe-west12': 'Turín, Italia',
    'europe-north1': 'Finlandia',
    'europe-central2': 'Varsovia, Polonia',
    'europe-southwest1': 'Madrid, España'
  };
  return regionMap[region] || 'Ubicación desconocida';
}

function findClosestToFrance(availableRegions) {
  // Orden de preferencia basado en proximidad a París
  const proximityOrder = [
    'europe-west9',  // París (mismo lugar)
    'europe-west1',  // Bélgica (muy cerca)
    'europe-west2',  // Londres
    'europe-west4',  // Países Bajos
    'europe-west6',  // Zurich
    'europe-west3',  // Frankfurt
    'europe-southwest1', // Madrid
    'europe-west8',  // Milán
    'europe-west10', // Berlín
    'europe-west12', // Turín
    'europe-central2', // Varsovia
    'europe-north1'  // Finlandia
  ];
  
  for (const region of proximityOrder) {
    if (availableRegions.includes(region)) {
      return region;
    }
  }
  
  return availableRegions[0]; // Fallback
}

// Ejecutar el test
testAllEuropeanRegions().catch(console.error);
